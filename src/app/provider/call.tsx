import React, { useState, useRef, useEffect } from "react";
import {
  View,
  ActivityIndicator,
  Alert,
  StyleSheet,
  TouchableOpacity,
  BackHandler,
  Platform,
} from "react-native";
import CallKitService from "~/services/CallKitService";
import {
  EventType,
  VideoAspect,
  ZoomView,
  useZoom,
  ZoomVideoSdkUser,
  VideoResolution,
} from "@zoom/react-native-videosdk";
import axiosConfig from "~/services/axiosConfig";
import { useAuth } from "~/context/AuthContext";
import { useRoute, RouteProp } from "@react-navigation/native";
import {
  ArrowRight,
  Mic,
  MicOff,
  PhoneCall,
  PhoneOff,
  Video,
  VideoOff,
  Wifi,
} from "@tamagui/lucide-icons";
import { Button, Spinner, Text } from "tamagui";
import { useNavigation, useRouter } from "expo-router";
import { useNetworkMonitor } from "src/hooks/useNetworkMonitor";


const FILE_NAME = "ProviderCallView";
const logEvent = (
  level: string,
  status: string,
  message: string,
  metadata: object = {}
) => {
  axiosConfig.post("/log-event", {
    level,
    status,
    name: FILE_NAME,
    message,
    metadata,
  });
};

type ProviderCallRouteParams = {
  consultationId: string;
};

const ProviderCallNative = ({ goToDetails }: { goToDetails: () => void }) => {
  // Instrument component render
  logEvent("INFO", "COMPONENT_RENDER", "ProviderCallNative render");

  const {
    joinSession,
    leaveSession,
    addListener,
    session,
    audioHelper,
    videoHelper,
  } = useZoom();
  const { user } = useAuth();
  const route =
    useRoute<RouteProp<{ params: ProviderCallRouteParams }, "params">>();
  const { consultationId } = route.params;

  const [loading, setLoading] = useState<boolean>(true);
  const [callStarted, setCallStarted] = useState<boolean>(false);
  const [callEnded, setCallEnded] = useState<boolean>(false);
  const [sdkJWT, setSdkJWT] = useState<string>("");
  const [usersInSession, setUsersInSession] = useState<ZoomVideoSdkUser[]>([]);
  const [videoOn, setVideoOn] = useState<boolean>(true);
  const [audioOn, setAudioOn] = useState<boolean>(true);

  const [isRecovering, setIsRecovering] = useState(false);
  const [viewKey, setViewKey] = useState(0);

  const listeners = useRef<any[]>([]);

  const {
    currentNetworkStatus,
    isMonitoring,
    startNetworkMonitoring,
    stopNetworkMonitoring,
    processNetworkQuality,
    getNetworkQualityText,
    getNetworkQualityColor,
  } = useNetworkMonitor();

  useEffect(() => {
    const netListener = addListener(
      EventType.onUserVideoNetworkStatusChanged,
      async ({ user: netUser, result }) => {
        // Process network quality for monitoring
        console.log("Provider: Network event received", {
          uplink: result.uplinkNetworkQuality,
          downlink: result.downlinkNetworkQuality
        });
        await processNetworkQuality(netUser, result);

        const me = await session.getMySelf();
        if (netUser.userId === me.userId && result.uplinkNetworkQuality < 2) {
          logEvent(
            "WARN",
            "NETWORK_DROP",
            "Network quality low, restarting video"
          );
          setIsRecovering(true);
          await videoHelper.stopVideo();
          await videoHelper.startVideo();
          setIsRecovering(false);
          logEvent(
            "WARN",
            "NETWORK_RECOVER",
            "Video restarted after network drop"
          );
        }
      }
    );
    const canvasFail = addListener(
      EventType.onVideoCanvasSubscribeFail,
      ({ canvasId }) => {
        logEvent(
          "WARN",
          "CANVAS_SUBSCRIBE_FAIL",
          `Canvas ${canvasId} failed, remounting view`
        );
        setViewKey((k) => k + 1);
      }
    );
    return () => {
      netListener.remove();
      canvasFail.remove();
    };
  }, []);

  const toggleAudio = async () => {
    logEvent("INFO", "TOGGLE_AUDIO", "toggleAudio called");
    try {
      const mySelf = await session.getMySelf();
      const muted = await mySelf.audioStatus.isMuted();
      if (muted) {
        await audioHelper.unmuteAudio(mySelf.userId);
      } else {
        await audioHelper.muteAudio(mySelf.userId);
      }
      logEvent("INFO", "AUDIO_TOGGLED", `Audio on: ${!muted}`);
    } catch (err) {
      logEvent(
        "ERROR",
        "TOGGLE_AUDIO_ERROR",
        "Error toggling audio",
        err as object
      );
    }
  };

  const toggleVideo = async () => {
    logEvent("INFO", "TOGGLE_VIDEO", "toggleVideo called");
    try {
      const mySelf = await session.getMySelf();
      const currentVideoOn = await mySelf.videoStatus.isOn();
      if (currentVideoOn) {
        await videoHelper.stopVideo();
      } else {
        await videoHelper.startVideo();
      }
      logEvent("INFO", "VIDEO_TOGGLED", `Video on: ${!currentVideoOn}`);
    } catch (err) {
      logEvent(
        "ERROR",
        "TOGGLE_VIDEO_ERROR",
        "Error toggling video",
        err as object
      );
    }
  };

  const joinSessionHandler = async () => {
    logEvent("INFO", "JOIN_SESSION", "joinSessionHandler called");
    if (!consultationId) {
      logEvent("ERROR", "MISSING_PARAMS", "Consultation ID is missing");
      Alert.alert("Error", "Consultation ID is missing.");
      setLoading(false);
      return;
    }
    setLoading(true);

    if (Platform.OS === "ios") {
      try {
        logEvent(
          "INFO",
          "CALLKIT_END",
          "Ending active CallKit calls before starting Zoom session"
        );
        const result = await CallKitService.endCurrentCallKitCall();
        logEvent(
          "INFO",
          "CALLKIT_END_RESULT",
          `CallKit calls ended: ${result}`
        );
      } catch (err) {
        logEvent(
          "ERROR",
          "CALLKIT_END_ERROR",
          "Error ending CallKit call",
          err as object
        );
        console.error("Failed to end CallKit calls:", err);
      }
    }

    try {
      const response = await axiosConfig.put(
        `/consultation/start/${consultationId}`
      );
      const { sdkJWT } = response.data;
      setSdkJWT(sdkJWT);
      const name = `${user?.firstName} ${user?.lastName.split("")[0]}`;
      await joinSession({
        sessionName: consultationId,
        userName: name,
        token: sdkJWT,
        sessionIdleTimeoutMins: 10,
        audioOptions: {
          connect: true,
          mute: false,
          autoAdjustSpeakerVolume: false,
        },
        videoOptions: { localVideoOn: true },
      });
      setCallStarted(true);
      logEvent("INFO", "JOIN_SESSION_SUCCESS", "Joined session successfully");

      const sessionJoinListener = addListener(
        EventType.onSessionJoin,
        async () => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          setUsersInSession([mySelf, ...remoteUsers]);

          // Start network monitoring when session is joined
          console.log("Provider: Starting network monitoring");
          startNetworkMonitoring();

          logEvent("INFO", "SESSION_JOINED", "onSessionJoin fired", {
            userCount: remoteUsers.length + 1,
          });
        }
      );
      listeners.current.push(sessionJoinListener);

      const userJoinListener = addListener(EventType.onUserJoin, async () => {
        const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
        const remoteUsers = await session.getRemoteUsers();
        setUsersInSession([mySelf, ...remoteUsers]);
        logEvent("INFO", "USER_JOIN", "onUserJoin fired", {
          remoteCount: remoteUsers.length,
        });
      });
      listeners.current.push(userJoinListener);

      const userLeaveListener = addListener(
        EventType.onUserLeave,
        async (event) => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          setUsersInSession([mySelf, ...remoteUsers]);
          logEvent("INFO", "USER_LEAVE", "onUserLeave fired", {
            remoteCount: remoteUsers.length,
          });
          if (remoteUsers.length === 0) {
            setCallEnded(true);
            logEvent("INFO", "SESSION_EMPTY", "No remote users, call ended");
          }
        }
      );
      listeners.current.push(userLeaveListener);

      const userAudioListener = addListener(
        EventType.onUserAudioStatusChanged,
        async (event) => {
          const { changedUsers } = event;
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          if (changedUsers.find((u) => u.userId === mySelf.userId)) {
            const muted = await mySelf.audioStatus.isMuted();
            setAudioOn(!muted);
            logEvent(
              "INFO",
              "USER_AUDIO_STATUS_CHANGED",
              `Local audio muted: ${muted}`
            );
          }
        }
      );
      listeners.current.push(userAudioListener);

      const userVideoListener = addListener(
        EventType.onUserVideoStatusChanged,
        async (event) => {
          logEvent(
            "INFO",
            "USER_VIDEO_STATUS_CHANGED",
            "onUserVideoStatusChanged fired",
            event
          );
          const { changedUsers } = event;
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          if (changedUsers.find((u) => u.userId === mySelf.userId)) {
            const on = await mySelf.videoStatus.isOn();
            setVideoOn(on);
            logEvent(
              "INFO",
              "USER_VIDEO_STATUS_CHANGED",
              `Local video on: ${on}`
            );
          }
        }
      );
      listeners.current.push(userVideoListener);

      const sessionLeaveListener2 = addListener(
        EventType.onSessionLeave,
        () => {
          // Stop network monitoring and log call summary
          const networkSummary = stopNetworkMonitoring();
          if (networkSummary) {
            logEvent("INFO", "CALL_NETWORK_SUMMARY", "Call network quality summary", {
              duration: Math.round((networkSummary.callEndTime! - networkSummary.callStartTime) / 1000),
              averageQuality: networkSummary.averageNetworkQuality.toFixed(2),
              networkDrops: networkSummary.networkDrops,
              poorNetworkDuration: Math.round(networkSummary.poorNetworkDuration),
              overallQuality: networkSummary.overallCallQuality,
              dataPoints: networkSummary.networkQualityHistory.length,
            });
          }

          setCallStarted(false);
          setUsersInSession([]);
          listeners.current.forEach((l) => l.remove());
          listeners.current = [];
          logEvent("INFO", "SESSION_LEAVE", "onSessionLeave fired");
        }
      );
      listeners.current.push(sessionLeaveListener2);

      const sessionError = addListener(EventType.onError, (event) => {
        logEvent("ERROR", "SESSION_ERROR", "onError fired", event as object);
        sessionLeaveListener2.remove();
      });
      listeners.current.push(sessionError);
    } catch (err) {
      logEvent(
        "ERROR",
        "JOIN_SESSION_ERROR",
        "Error joining session",
        err as object
      );
      Alert.alert("Error", "Failed to join the session");
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveSession = async () => {
    logEvent("INFO", "LEAVE_SESSION", "handleLeaveSession called");

    // Stop network monitoring and log call summary before leaving
    const networkSummary = stopNetworkMonitoring();
    if (networkSummary) {
      logEvent("INFO", "CALL_NETWORK_SUMMARY", "Call network quality summary", {
        duration: Math.round((networkSummary.callEndTime! - networkSummary.callStartTime) / 1000),
        averageQuality: networkSummary.averageNetworkQuality.toFixed(2),
        networkDrops: networkSummary.networkDrops,
        poorNetworkDuration: Math.round(networkSummary.poorNetworkDuration),
        overallQuality: networkSummary.overallCallQuality,
        dataPoints: networkSummary.networkQualityHistory.length,
      });
    }

    try {
      await leaveSession();
      setCallStarted(false);
      setUsersInSession([]);
      listeners.current.forEach((l) => l.remove());
      listeners.current = [];
      logEvent("INFO", "LEAVE_SESSION_SUCCESS", "Left session successfully");
    } catch (err) {
      logEvent(
        "ERROR",
        "LEAVE_SESSION_ERROR",
        "Error leaving session",
        err as object
      );
      Alert.alert("Error", "Failed to leave the session");
    } finally {
      setCallEnded(true);
      goToDetails();
    }
  };

  const router = useRouter();
  const callDetails = () => {
    goToDetails();
    logEvent("INFO", "NAVIGATE_CALL_DETAILS", "Navigating to call details");
  };

  useEffect(() => {
    logEvent("INFO", "COMPONENT_MOUNT", "ProviderCallNative mounted");
    joinSessionHandler();
    return () => {
      logEvent("INFO", "COMPONENT_UNMOUNT", "ProviderCallNative unmounted");
    };
  }, []);

  useEffect(() => {
    if (callEnded) {
      handleLeaveSession();
    }
  }, [callEnded]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      () => {
        logEvent("INFO", "BACK_BUTTON_PRESS", "Hardware back pressed");
        return true;
      }
    );
    return () => backHandler.remove();
  }, []);

  const navigation = useNavigation();
  useEffect(() => {
    const unsubscribe = navigation.addListener("beforeRemove", (e) => {
      if (!callEnded) {
        e.preventDefault();
        handleLeaveSession().then(() => {
          navigation.dispatch(e.data.action);
        });
      }
    });
    return unsubscribe;
  }, [callEnded, navigation]);

  const callDetailsScreen = () => {
    goToDetails();
    // router.replace({
    //   pathname: "/provider/CallDetails",
    //   params: {
    //     consultationId,
    //     isComingFromCall: "false",
    //     shouldNotGoBack: "true",
    //   },
    // });
  };

  useEffect(() => {
    return () => {
      listeners.current.forEach((listener) => listener.remove());
      listeners.current = [];
    };
  }, []);

  // Render
  if (loading || isRecovering) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
      </View>
    );
  }

  // Separate local and remote users for rendering
  const localUser = usersInSession.find((user) => user.isHost);
  const remoteUsers = usersInSession.filter((user) => !user.isHost);

  return (
    <View style={styles.container}>
      {callEnded ? (
        <View style={styles.callEndedContainer}>
          <PhoneCall size={24} color="#ffffff" onPress={callDetails} />
          <Button onPress={callDetailsScreen} color="#0e71eb" size="$4" mt="$2">
            <Text>Call Details</Text>
          </Button>
        </View>
      ) : (
        <>
          <View style={styles.topSpacer} />

          {/* Network Quality Indicator */}
          {isMonitoring && (
            <View style={styles.networkIndicator}>
              <Wifi
                size={16}
                color={getNetworkQualityColor(currentNetworkStatus) as any}
              />
              <Text style={[
                styles.networkText,
                { color: getNetworkQualityColor(currentNetworkStatus) }
              ]}>
                {getNetworkQualityText(currentNetworkStatus)}
              </Text>
            </View>
          )}

          <View style={styles.videoContainer}>
            {/* Render Remote Users' Video */}
            {remoteUsers.length > 0 ? (
              remoteUsers.map((user) => (
                <View key={user.userId} style={styles.videoWrapper}>
                  <ZoomView
                    // style={{ position: "absolute", top: 0, left: 0, right: 0, bottom: 0, zIndex: 10 }} // maybe?
                    key={viewKey}
                    style={styles.zoomView}
                    userId={user.userId}
                    fullScreen={false}
                    videoAspect={VideoAspect.PanAndScan}
                    videoResolution={VideoResolution.ResolutionAuto}
                  />
                </View>
              ))
            ) : (
              <View style={styles.placeholder}>
                <Spinner size="large" />
              </View>
            )}
            {/* Render Local User's Video */}
            {localUser && (
              <View style={styles.videoWrapper}>
                <ZoomView
                  key={viewKey + 1}
                  style={styles.zoomView}
                  userId={localUser.userId}
                  fullScreen={false}
                  videoAspect={VideoAspect.PanAndScan}
                  videoResolution={VideoResolution.ResolutionAuto}
                />
              </View>
            )}
          </View>

          {callStarted && (
            <View style={styles.controlsContainer}>
              <TouchableOpacity
                style={styles.controlButton}
                onPress={toggleVideo}
              >
                {videoOn ? (
                  <Video size={24} color="#ffffff" />
                ) : (
                  <VideoOff size={24} color="#ffffff" />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.controlButton}
                onPress={toggleAudio}
              >
                {audioOn ? (
                  <Mic size={24} color="#ffffff" />
                ) : (
                  <MicOff size={24} color="#ffffff" />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.controlButton, styles.endCallButton]}
                onPress={handleLeaveSession}
              >
                <PhoneOff size={24} color="#ffffff" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.controlButton}
                onPress={callDetails}
              >
                <ArrowRight size={24} color="#ffffff" />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.controlButton}
                onPress={() => {
                  logEvent(
                    "INFO",
                    "MANUAL_RETRY",
                    "User tapped retry in controls"
                  );
                  setViewKey((v) => v + 1);
                }}
              >
                <Text style={styles.retryText}>Retry</Text>
              </TouchableOpacity>
            </View>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  callEndedContainer: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: "#1a1a1a", // Dark background for a modern look
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.8)", // Semi-transparent overlay
  },
  topSpacer: {
    flex: 0, // Reduced to 5% for better space utilization
  },
  videoContainer: {
    flex: 0.9, // 75% of screen height for video
    padding: 10,
  },
  videoWrapper: {
    flex: 1, // Each video takes half the container when both are present
    marginVertical: 5,
    borderRadius: 10,
    overflow: "hidden",
    position: "relative",
    backgroundColor: "#333", // Fallback background for video
  },
  zoomView: {
    width: "100%",
    height: "100%",
  },
  placeholder: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#333",
    borderRadius: 10,
    marginVertical: 5,
  },
  placeholderText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "500",
  },
  controlsContainer: {
    flex: 0.1,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: 20,
    backgroundColor: "rgba(0, 0, 0, 0.9)",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginInline: 10,
  },
  controlButton: {
    padding: 15,
    backgroundColor: "#555",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  retryText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  endCallButton: {
    backgroundColor: "#ff4444",
  },
  networkIndicator: {
    position: "absolute",
    top: 50,
    right: 20,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    zIndex: 1000,
  },
  networkText: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: "600",
  },
});

export default ProviderCallNative;
