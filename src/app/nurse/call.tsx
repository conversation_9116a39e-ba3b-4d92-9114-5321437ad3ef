import { useRef, useState, useEffect } from "react";
import {
  ActivityIndicator,
  EmitterSubscription,
  Platform,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import CallKitService from "~/services/CallKitService";
import { <PERSON>ton, Image, Spinner, Text, View, YStack } from "tamagui";
import {
  Errors,
  EventType,
  LiveTranscriptionStatus,
  VideoAspect,
  VideoResolution,
  ZoomVideoSdkLiveTranscriptionMessageInfo,
  ZoomVideoSdkLiveTranscriptionMessageInfoType,
  ZoomVideoSdkUser,
  ZoomView,
  useZoom,
} from "@zoom/react-native-videosdk";
import { useRoute, RouteProp } from "@react-navigation/native";
import { useAuth } from "../../context/AuthContext";
import { useDashboardStyles } from "./Styles/DashboardStyle";
import {
  ArrowRight,
  Mic,
  MicOff,
  PhoneOff,
  Video,
  VideoOff,
  Wifi,
} from "@tamagui/lucide-icons";
import { useRouter } from "expo-router";
import * as FileSystem from "expo-file-system";
import useSendTranscript from "src/hooks/useSendConsultation";
import axiosConfig from "src/services/axiosConfig";
import { useNetworkMonitor } from "../../hooks/useNetworkMonitor";

const FILE_NAME = "NurseCallView";
const logEvent = (
  level: string,
  status: string,
  message: string,
  metadata: object = {}
) => {
  axiosConfig.post("/log-event", {
    level,
    status,
    name: FILE_NAME,
    message,
    metadata,
  });
};

type NurseCallRouteParams = {
  params: {
    consultationId: string;
    sdkId: string;
  };
};

const NurseCallView = () => {
  logEvent("INFO", "COMPONENT_RENDER", "NurseCallView render");
  const dashboardStyles = useDashboardStyles();
  const { container, mainStack } = dashboardStyles;
  const {
    joinSession,
    leaveSession,
    addListener,
    session,
    audioHelper,
    videoHelper,
    liveTranscriptionHelper,
  } = useZoom();
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const listeners = useRef<EmitterSubscription[]>([]);
  const [users, setUsersInSession] = useState<ZoomVideoSdkUser[]>([]);
  const [isInSession, setIsInSession] = useState(false);
  const [isAudioMuted, setIsAudioMuted] = useState(true);
  const [isVideoMuted, setIsVideoMuted] = useState(true);
  const [callStarted, setCallStarted] = useState<boolean>(false);
  const { sendTranscript, error, responseData } = useSendTranscript();
  const [transcriptionMessages, setTranscriptionMessages] = useState<{
    mine: { content: string; timestamp: number }[];
    others: { content: string; timestamp: number }[];
  }>({ mine: [], others: [] });
  const [isRecovering, setIsRecovering] = useState(false);
  const [viewKey, setViewKey] = useState(0);

  const route = useRoute<RouteProp<NurseCallRouteParams, "params">>();
  const { consultationId, sdkId } = route.params;
  const router = useRouter();

  // Network monitoring
  const {
    currentNetworkStatus,
    isMonitoring,
    startNetworkMonitoring,
    stopNetworkMonitoring,
    processNetworkQuality,
    getNetworkQualityText,
    getNetworkQualityColor,
  } = useNetworkMonitor();

  useEffect(() => {
    const netListener = addListener(
      EventType.onUserVideoNetworkStatusChanged,
      async ({ user: netUser, result }) => {
        // Process network quality for monitoring
        await processNetworkQuality(netUser, result);

        const me = await session.getMySelf();
        if (netUser.userId === me.userId && result.uplinkNetworkQuality < 2) {
          logEvent(
            "WARN",
            "NETWORK_DROP",
            "Network quality low, restarting video"
          );
          setIsRecovering(true);
          await videoHelper.stopVideo();
          await videoHelper.startVideo();
          setIsRecovering(false);
          logEvent(
            "WARN",
            "NETWORK_RECOVER",
            "Video restarted after network drop"
          );
        }
      }
    );

    // Re-mount ZoomView on canvas subscribe fail
    const canvasFail = addListener(
      EventType.onVideoCanvasSubscribeFail,
      ({ canvasId }) => {
        logEvent(
          "WARN",
          "CANVAS_SUBSCRIBE_FAIL",
          `Canvas ${canvasId} failed, remounting view`
        );
        setViewKey((k) => k + 1);
      }
    );

    return () => {
      netListener.remove();
      canvasFail.remove();
    };
  }, []);

  useEffect(() => {
    logEvent("INFO", "COMPONENT_MOUNT", "NurseCallView mounted");
    onJoinZoomSession();
    return () => {
      logEvent("INFO", "COMPONENT_UNMOUNT", "NurseCallView unmounted");
    };
  }, []);

  const toggleAudio = async () => {
    logEvent("INFO", "TOGGLE_AUDIO", "toggleAudio called");
    try {
      const mySelf = await session.getMySelf();
      const muted = await mySelf.audioStatus.isMuted();
      if (muted) {
        await audioHelper.unmuteAudio(mySelf.userId);
      } else {
        await audioHelper.muteAudio(mySelf.userId);
      }
      logEvent("INFO", "AUDIO_TOGGLED", `Audio muted: ${!muted}`);
    } catch (error) {
      logEvent(
        "ERROR",
        "TOGGLE_AUDIO_ERROR",
        "Error toggling audio",
        error as object
      );
    }
  };

  const toggleVideo = async () => {
    logEvent("INFO", "TOGGLE_VIDEO", "toggleVideo called");
    try {
      const mySelf = await session.getMySelf();
      const videoOn = await mySelf.videoStatus.isOn();
      if (videoOn) {
        await videoHelper.stopVideo();
      } else {
        await videoHelper.startVideo();
      }
      logEvent("INFO", "VIDEO_TOGGLED", `Video on: ${!videoOn}`);
    } catch (error) {
      logEvent(
        "ERROR",
        "TOGGLE_VIDEO_ERROR",
        "Error toggling video",
        error as object
      );
    }
  };

  const startTranscription = async () => {
    logEvent(
      "INFO",
      "START_TRANSCRIPTION",
      "Attempting to start transcription"
    );
    try {
      const canStart =
        await liveTranscriptionHelper.canStartLiveTranscription();
      if (canStart) {
        const result = await liveTranscriptionHelper.startLiveTranscription();
        if (result === Errors.Success) {
          logEvent(
            "INFO",
            "TRANSCRIPTION_STARTED",
            "Live transcription started"
          );

          const liveTranscriptionStatusChangeListener = addListener(
            EventType.onLiveTranscriptionStatus,
            ({ status }: { status: LiveTranscriptionStatus }) => {
              logEvent(
                "INFO",
                "TRANSCRIPTION_STATUS_CHANGED",
                `Status: ${status}`
              );
            }
          );
          listeners.current.push(liveTranscriptionStatusChangeListener);

          await liveTranscriptionHelper.setSpokenLanguage(0);
          await liveTranscriptionHelper.setTranslationLanguage(0);

          const liveTranscriptionMsgInfoReceivedListener = addListener(
            EventType.onLiveTranscriptionMsgInfoReceived,
            ({
              messageInfo,
            }: {
              messageInfo: ZoomVideoSdkLiveTranscriptionMessageInfoType;
            }) => {
              const message = new ZoomVideoSdkLiveTranscriptionMessageInfo(
                messageInfo
              );
              logEvent(
                "INFO",
                "TRANSCRIPTION_MSG_RECEIVED",
                message.messageContent || ""
              );
            }
          );
          listeners.current.push(liveTranscriptionMsgInfoReceivedListener);

          await liveTranscriptionHelper.enableReceiveSpokenLanguageContent(
            true
          );

          const originalLanguageMsgInfoReceivedListener = addListener(
            EventType.onOriginalLanguageMsgReceived,
            ({
              messageInfo,
            }: {
              messageInfo: ZoomVideoSdkLiveTranscriptionMessageInfoType;
            }) => {
              const message = new ZoomVideoSdkLiveTranscriptionMessageInfo(
                messageInfo
              );
              logEvent(
                "INFO",
                "ORIGINAL_TRANSCRIPTION_MSG",
                message.messageContent || ""
              );
            }
          );
          listeners.current.push(originalLanguageMsgInfoReceivedListener);
        } else {
          logEvent(
            "ERROR",
            "TRANSCRIPTION_FAILED",
            `Failed to start transcription: ${result}`,
            { result }
          );
        }
      } else {
        logEvent(
          "ERROR",
          "TRANSCRIPTION_PERMISSION_DENIED",
          "Cannot start transcription"
        );
      }
    } catch (error) {
      logEvent(
        "ERROR",
        "TRANSCRIPTION_ERROR",
        "Error in startTranscription",
        error as object
      );
    }
  };

  const fetchTranscriptionMessages = async () => {
    logEvent("INFO", "FETCH_TRANSCRIPTIONS", "Fetching transcription messages");
    try {
      const messages =
        await liveTranscriptionHelper.getHistoryTranslationMessageList();
      const formattedTranscript = messages.map(
        (msg: ZoomVideoSdkLiveTranscriptionMessageInfo) => {
          const isNurse = msg.speakerName === "Nurse";
          return {
            speaker: isNurse ? "nurse" : "provider",
            text: msg.messageContent || "N/A",
            timestamp: new Date(Number(msg.timeStamp) * 1000).toISOString(),
          };
        }
      );
      formattedTranscript.sort(
        (a: { timestamp: string }, b: { timestamp: string }) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
      await sendTranscript(consultationId, formattedTranscript);
      logEvent("INFO", "TRANSCRIPTIONS_SENT", "Transcriptions sent", {
        count: formattedTranscript.length,
      });
    } catch (error) {
      logEvent(
        "ERROR",
        "FETCH_TRANSCRIPTIONS_ERROR",
        "Error fetching or sending transcription",
        error as object
      );
    }
  };

  const onJoinZoomSession = async () => {
    logEvent("INFO", "JOIN_SESSION", "onJoinZoomSession called");
    if (!consultationId || !sdkId) {
      logEvent("ERROR", "MISSING_PARAMS", "Missing consultationId or sdkJWT");
      return;
    }

    setLoading(true);
    if (Platform.OS === "ios") {
      try {
        logEvent(
          "INFO",
          "CALLKIT_END",
          "Ending any active CallKit calls before starting Zoom session"
        );
        const result = await CallKitService.endCurrentCallKitCall();
        logEvent(
          "INFO",
          "CALLKIT_END_RESULT",
          `CallKit calls ended: ${result}`
        );
      } catch (error) {
        logEvent(
          "ERROR",
          "CALLKIT_END_ERROR",
          "Error ending CallKit call",
          error as object
        );
        console.error("Failed to end CallKit calls:", error);
      }
    }

    const name = `${user?.firstName} ${user?.lastName?.charAt(0)}`;
    try {
      await joinSession({
        sessionName: consultationId,
        userName: name,
        token: sdkId,
        sessionIdleTimeoutMins: 10,
        audioOptions: {
          connect: true,
          mute: false,
          autoAdjustSpeakerVolume: false,
        },
        videoOptions: { localVideoOn: true },
        // Note: We're disabling CallKit at the provider level in _layout.tsx
      });
      logEvent("INFO", "JOIN_SESSION_SUCCESS", "joinSession successful");
    } catch (error) {
      logEvent(
        "ERROR",
        "JOIN_SESSION_ERROR",
        "Error joining session",
        error as object
      );
    } finally {
      setLoading(false);
    }
    setCallStarted(true);

    const sessionJoin = addListener(EventType.onSessionJoin, async () => {
      const mySelfUser = new ZoomVideoSdkUser(await session.getMySelf());
      const remoteUsers = await session.getRemoteUsers();
      setUsersInSession([mySelfUser, ...remoteUsers]);
      setIsInSession(true);

      // Start network monitoring when session is joined
      startNetworkMonitoring();

      logEvent("INFO", "SESSION_JOINED", "Session joined", {
        userCount: remoteUsers.length + 1,
      });
      await startTranscription();
    });
    listeners.current.push(sessionJoin);

    const userJoin = addListener(EventType.onUserJoin, async (event) => {
      const { remoteUsers } = event;
      const mySelfUser = await session.getMySelf();
      const remote = remoteUsers.map((u) => new ZoomVideoSdkUser(u));
      setUsersInSession([new ZoomVideoSdkUser(mySelfUser), ...remote]);
      logEvent("INFO", "USER_JOIN", "User joined", {
        remoteCount: remoteUsers.length,
      });
    });
    listeners.current.push(userJoin);

    const userLeave = addListener(EventType.onUserLeave, async (event) => {
      const remoteUsers = await session.getRemoteUsers();
      setUsersInSession([
        new ZoomVideoSdkUser(await session.getMySelf()),
        ...remoteUsers,
      ]);
      logEvent("INFO", "USER_LEAVE", "User left", {
        remoteCount: remoteUsers.length,
      });
      if (remoteUsers.length === 0) {
        logEvent("INFO", "SESSION_EMPTY", "No remote users, leaving session");
        onLeaveSession();
      }
    });
    listeners.current.push(userLeave);

    const userVideo = addListener(
      EventType.onUserVideoStatusChanged,
      async (event) => {
        const mySelfUser = new ZoomVideoSdkUser(await session.getMySelf());
        const changed = event.changedUsers.find(
          (u) => u.userId === mySelfUser.userId
        );
        if (changed) {
          const on = await mySelfUser.videoStatus.isOn();
          setIsVideoMuted(!on);
          logEvent(
            "INFO",
            "USER_VIDEO_STATUS_CHANGED",
            `Local video on: ${on}`
          );
        }
      }
    );
    listeners.current.push(userVideo);

    const userAudio = addListener(
      EventType.onUserAudioStatusChanged,
      async (event) => {
        const mySelfUser = new ZoomVideoSdkUser(await session.getMySelf());
        const changed = event.changedUsers.find(
          (u) => u.userId === mySelfUser.userId
        );
        if (changed) {
          const muted = await mySelfUser.audioStatus.isMuted();
          setIsAudioMuted(muted);
          logEvent(
            "INFO",
            "USER_AUDIO_STATUS_CHANGED",
            `Local audio muted: ${muted}`
          );
        }
      }
    );
    listeners.current.push(userAudio);

    const sessionLeave = addListener(EventType.onSessionLeave, () => {
      // Stop network monitoring and log call summary
      const networkSummary = stopNetworkMonitoring();
      if (networkSummary) {
        logEvent("INFO", "CALL_NETWORK_SUMMARY", "Call network quality summary", {
          duration: Math.round((networkSummary.callEndTime! - networkSummary.callStartTime) / 1000),
          averageQuality: networkSummary.averageNetworkQuality.toFixed(2),
          networkDrops: networkSummary.networkDrops,
          poorNetworkDuration: Math.round(networkSummary.poorNetworkDuration),
          overallQuality: networkSummary.overallCallQuality,
          dataPoints: networkSummary.networkQualityHistory.length,
        });
      }

      setCallStarted(false);
      setIsInSession(false);
      setUsersInSession([]);
      setTranscriptionMessages({ mine: [], others: [] });
      logEvent("INFO", "SESSION_LEFT", "Session leave event");
      sessionLeave.remove();
    });
    listeners.current.push(sessionLeave);

    const sessionError = addListener(EventType.onError, (event) => {
      logEvent("ERROR", "SESSION_ERROR", "Session error", event as object);
      sessionLeave.remove();
    });
    listeners.current.push(sessionError);
  };

  // Leave session and fetch transcription once
  const onLeaveSession = async () => {
    logEvent("INFO", "LEAVE_SESSION", "Leaving session");
    await fetchTranscriptionMessages();
    await liveTranscriptionHelper.stopLiveTranscription();
    leaveSession(false);
    setIsInSession(false);
    setCallStarted(false);
    listeners.current.forEach((listener) => listener.remove());
    listeners.current = [];
    logEvent("INFO", "CALL_OVERVIEW", "Navigating to call overview");
    callOverView();
  };

  const localUser = users.find((user) => !user.isHost);
  const remoteUsers = users.filter((user) => user.isHost);

  const callOverView = () => {
    router.replace({
      pathname: "/nurse/calloverview",
      params: { consultationId },
    });
  };

  return (
    <View style={styles.container}>
      {loading || isRecovering ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
        </View>
      ) : (
        <>
          <View style={styles.topSpacer} />

          {/* Network Quality Indicator */}
          {isMonitoring && (
            <View style={styles.networkIndicator}>
              <Wifi
                size={16}
                color={getNetworkQualityColor(currentNetworkStatus) as any}
              />
              <Text style={[
                styles.networkText,
                { color: getNetworkQualityColor(currentNetworkStatus) }
              ]}>
                {getNetworkQualityText(currentNetworkStatus)}
              </Text>
            </View>
          )}

          <View style={styles.videoContainer}>
            {/* Remote User (Caller) Video */}
            {callStarted && users.filter((u) => u.isHost).length > 0 ? (
              users
                .filter((u) => u.isHost)
                .map((user) => (
                  <View key={user.userId} style={styles.videoWrapper}>
                    <ZoomView
                      key={viewKey}
                      style={styles.zoomView}
                      userId={user.userId}
                      fullScreen={false}
                      videoAspect={VideoAspect.PanAndScan}
                      videoResolution={VideoResolution.ResolutionAuto}
                    />
                  </View>
                ))
            ) : (
              <View style={styles.placeholder}>
                <Spinner size="large" />
              </View>
            )}

            {/* Local User (Provider) Video */}
            {callStarted && users.find((u) => !u.isHost) && (
              <View style={styles.videoWrapper}>
                <ZoomView
                  key={viewKey + 1}
                  style={styles.zoomView}
                  userId={users.find((u) => !u.isHost)!.userId}
                  fullScreen={false}
                  videoAspect={VideoAspect.PanAndScan}
                  videoResolution={VideoResolution.ResolutionAuto}
                />
              </View>
            )}
          </View>

          {callStarted && (
            <View style={styles.controlsContainer}>
              <TouchableOpacity
                style={styles.controlButton}
                onPress={toggleVideo}
              >
                {!isVideoMuted ? (
                  <Video size={24} color="#ffffff" />
                ) : (
                  <VideoOff size={24} color="#ffffff" />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.controlButton}
                onPress={toggleAudio}
              >
                {!isAudioMuted ? (
                  <Mic size={24} color="#ffffff" />
                ) : (
                  <MicOff size={24} color="#ffffff" />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.controlButton, styles.endCallButton]}
                onPress={onLeaveSession}
              >
                <PhoneOff size={24} color="#ffffff" />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.controlButton}
                onPress={() => {
                  logEvent(
                    "INFO",
                    "MANUAL_RETRY",
                    "User tapped retry in controls"
                  );
                  setViewKey((v) => v + 1);
                }}
              >
                <Text style={styles.retryText}>Retry</Text>
              </TouchableOpacity>
            </View>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1a1a1a", // Dark background for a modern look
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.8)", // Semi-transparent overlay
  },
  topSpacer: {
    flex: 0, // Reduced to 5% for better space utilization
  },
  videoContainer: {
    flex: 0.9, // 75% of screen height for video
    padding: 10,
  },
  videoWrapper: {
    flex: 1, // Each video takes half the container when both are present
    marginVertical: 5,
    borderRadius: 10,
    overflow: "hidden",
    position: "relative",
    backgroundColor: "#333", // Fallback background for video
  },
  zoomView: {
    width: "100%",
    height: "100%",
  },
  placeholder: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#333",
    borderRadius: 10,
    marginVertical: 5,
  },
  placeholderText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "500",
  },
  controlsContainer: {
    flex: 0.1,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: 20,
    backgroundColor: "rgba(0, 0, 0, 0.9)",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginInline: 10,
  },
  controlButton: {
    padding: 15,
    backgroundColor: "#555",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  retryText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  endCallButton: {
    backgroundColor: "#ff4444",
  },
  networkIndicator: {
    position: "absolute",
    top: 50,
    right: 20,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    zIndex: 1000,
  },
  networkText: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: "600",
  },
});

export default NurseCallView;
