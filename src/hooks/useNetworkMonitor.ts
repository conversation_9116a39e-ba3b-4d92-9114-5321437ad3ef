import { useEffect, useRef, useState } from "react";
import { EmitterSubscription } from "react-native";
import {
  EventType,
  NetworkStatus,
  useZoom,
} from "@zoom/react-native-videosdk";

export interface NetworkQualityData {
  timestamp: number;
  networkStatus: NetworkStatus;
  uplinkNetworkQuality: number;
  downlinkNetworkQuality: number;
  connectionType?: string;
  signalStrength?: number;
}

export interface CallNetworkSummary {
  callStartTime: number;
  callEndTime?: number;
  averageNetworkQuality: number;
  networkDrops: number;
  poorNetworkDuration: number;
  networkQualityHistory: NetworkQualityData[];
  overallCallQuality: "excellent" | "good" | "fair" | "poor";
}

export const useNetworkMonitor = () => {
  const { addListener, session } = useZoom();
  const [currentNetworkStatus, setCurrentNetworkStatus] = useState<NetworkStatus>(NetworkStatus.None);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [callNetworkData, setCallNetworkData] = useState<CallNetworkSummary | null>(null);

  const listeners = useRef<EmitterSubscription[]>([]);
  const networkHistory = useRef<NetworkQualityData[]>([]);
  const callStartTime = useRef<number>(0);
  const lastPoorNetworkTime = useRef<number>(0);
  const totalPoorNetworkDuration = useRef<number>(0);
  const networkDropCount = useRef<number>(0);

  const getNetworkStatusFromQuality = (quality: number): NetworkStatus => {
    if (quality >= 3) return NetworkStatus.Good;
    if (quality >= 2) return NetworkStatus.Normal;
    if (quality >= 1) return NetworkStatus.Bad;
    return NetworkStatus.None;
  };

  const calculateOverallCallQuality = (
    avgQuality: number,
    drops: number,
    poorDuration: number,
    callDuration: number
  ): "excellent" | "good" | "fair" | "poor" => {
    const poorNetworkPercentage = (poorDuration / callDuration) * 100;

    if (avgQuality >= 3 && drops <= 1 && poorNetworkPercentage < 5) {
      return "excellent";
    } else if (avgQuality >= 2.5 && drops <= 3 && poorNetworkPercentage < 15) {
      return "good";
    } else if (avgQuality >= 2 && drops <= 5 && poorNetworkPercentage < 30) {
      return "fair";
    } else {
      return "poor";
    }
  };

  const startNetworkMonitoring = () => {
    if (isMonitoring) return;

    console.log("Starting network quality monitoring");
    setIsMonitoring(true);
    callStartTime.current = Date.now();
    networkHistory.current = [];
    networkDropCount.current = 0;
    totalPoorNetworkDuration.current = 0;
    lastPoorNetworkTime.current = 0;
  };

  const stopNetworkMonitoring = (): CallNetworkSummary | null => {
    console.log("In here ",isMonitoring);
    if (!isMonitoring) return null;

    console.log("Stopping network quality monitoring");
    setIsMonitoring(false);

    listeners.current.forEach(listener => listener.remove());
    listeners.current = [];

    const callEndTime = Date.now();
    const callDuration = (callEndTime - callStartTime.current) / 1000; // in seconds

    if (lastPoorNetworkTime.current > 0) {
      totalPoorNetworkDuration.current += (callEndTime - lastPoorNetworkTime.current) / 1000;
      lastPoorNetworkTime.current = 0;
    }

    if (networkHistory.current.length === 0) {
      console.log("No network data collected during call");
      return null;
    }

    const totalQuality = networkHistory.current.reduce((sum, data) => {
      return sum + (data.uplinkNetworkQuality + data.downlinkNetworkQuality) / 2;
    }, 0);
    const averageNetworkQuality = totalQuality / networkHistory.current.length;

    const overallCallQuality = calculateOverallCallQuality(
      averageNetworkQuality,
      networkDropCount.current,
      totalPoorNetworkDuration.current,
      callDuration
    );

    const summary: CallNetworkSummary = {
      callStartTime: callStartTime.current,
      callEndTime,
      averageNetworkQuality,
      networkDrops: networkDropCount.current,
      poorNetworkDuration: totalPoorNetworkDuration.current,
      networkQualityHistory: [...networkHistory.current],
      overallCallQuality,
    };

    setCallNetworkData(summary);

    console.log("Call Network Quality Summary:", {
      duration: `${Math.round(callDuration)}s`,
      averageQuality: averageNetworkQuality.toFixed(2),
      networkDrops: networkDropCount.current,
      poorNetworkDuration: `${Math.round(totalPoorNetworkDuration.current)}s`,
      overallQuality: overallCallQuality,
      dataPoints: networkHistory.current.length,
    });

    return summary;
  };

  // Process network quality data (to be called from existing listeners)
  const processNetworkQuality = async (netUser: any, result: any) => {
    if (!isMonitoring) return;

    try {
      const me = await session.getMySelf();
      if (netUser.userId === me.userId) {
        const timestamp = Date.now();
        const uplinkQuality = result.uplinkNetworkQuality || 0;
        const downlinkQuality = result.downlinkNetworkQuality || 0;
        const avgQuality = (uplinkQuality + downlinkQuality) / 2;
        const networkStatus = getNetworkStatusFromQuality(avgQuality);

        // Track network drops
        if (avgQuality < 2 && networkHistory.current.length > 0) {
          const lastEntry = networkHistory.current[networkHistory.current.length - 1];
          if (lastEntry && (lastEntry.uplinkNetworkQuality + lastEntry.downlinkNetworkQuality) / 2 >= 2) {
            networkDropCount.current++;
            console.log(`Network drop detected. Total drops: ${networkDropCount.current}`);
          }
        }

        // Track poor network duration
        if (avgQuality < 2) {
          if (lastPoorNetworkTime.current === 0) {
            lastPoorNetworkTime.current = timestamp;
          }
        } else {
          if (lastPoorNetworkTime.current > 0) {
            totalPoorNetworkDuration.current += (timestamp - lastPoorNetworkTime.current) / 1000;
            lastPoorNetworkTime.current = 0;
          }
        }

        const networkData: NetworkQualityData = {
          timestamp,
          networkStatus,
          uplinkNetworkQuality: uplinkQuality,
          downlinkNetworkQuality: downlinkQuality,
        };

        networkHistory.current.push(networkData);
        setCurrentNetworkStatus(networkStatus);

        // Log network quality data
        console.log("Network Quality Update:", {
          status: networkStatus,
          uplink: uplinkQuality,
          downlink: downlinkQuality,
          average: avgQuality,
          drops: networkDropCount.current,
        });
      }
    } catch (error) {
      console.error("Error processing network status change:", error);
    }
  };

  // Get current network quality as a readable string
  const getNetworkQualityText = (status: NetworkStatus): string => {
    switch (status) {
      case NetworkStatus.Good:
        return "Excellent";
      case NetworkStatus.Normal:
        return "Good";
      case NetworkStatus.Bad:
        return "Poor";
      case NetworkStatus.None:
      default:
        return "Unknown";
    }
  };

  // Get network quality color for UI
  const getNetworkQualityColor = (status: NetworkStatus): string => {
    switch (status) {
      case NetworkStatus.Good:
        return "#4CAF50"; // Green
      case NetworkStatus.Normal:
        return "#FF9800"; // Orange
      case NetworkStatus.Bad:
        return "#F44336"; // Red
      case NetworkStatus.None:
      default:
        return "#9E9E9E"; // Gray
    }
  };

  useEffect(() => {
    return () => {
      listeners.current.forEach(listener => listener.remove());
    };
  }, []);

  return {
    currentNetworkStatus,
    isMonitoring,
    callNetworkData,
    startNetworkMonitoring,
    stopNetworkMonitoring,
    processNetworkQuality,
    getNetworkQualityText,
    getNetworkQualityColor,
    networkHistory: networkHistory.current,
  };
};
